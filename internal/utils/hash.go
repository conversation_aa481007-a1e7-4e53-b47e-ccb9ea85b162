package utils

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
)

// GenerateHash 生成数据的SHA256哈希值
func GenerateHash(data interface{}) (string, error) {
	// 将数据转换为JSON字符串以确保一致性
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal data to JSON: %w", err)
	}

	// 计算SHA256哈希
	hash := sha256.Sum256(jsonData)
	return hex.EncodeToString(hash[:]), nil
}

// GenerateCacheKey 生成缓存键
// 格式: quest:{hash_value}
func GenerateCacheKey(data interface{}) (string, error) {
	hash, err := GenerateHash(data)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("quest:%s", hash), nil
}

// GenerateCacheKeyFromCleanString 基于qwen_json_clean字符串生成缓存键
// 根据S9.md文档要求：将qwen_json_clean的内容进行哈希化，然后生成缓存键（quest:哈希值）
func GenerateCacheKeyFromCleanString(qwenJsonClean string) string {
	hash := GenerateHashFromString(qwenJsonClean)
	return fmt.Sprintf("quest:%s", hash)
}

// GenerateHashFromString 从字符串生成哈希值
func GenerateHashFromString(data string) string {
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}
