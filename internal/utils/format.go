package utils

import (
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"regexp"
	"sort"
	"strings"
)

// ParseQuestionJSON 解析Qwen返回的新格式JSON数据
// 根据S8.md文档的要求进行数据处理
func ParseQuestionJSON(qwenResponse string) (*model.QwenData, error) {
	// 解析新的JSON格式：qutext + options
	var rawData struct {
		QuText  string            `json:"qutext"`
		Options map[string]string `json:"options"`
	}

	if err := json.Unmarshal([]byte(qwenResponse), &rawData); err != nil {
		return nil, fmt.Errorf("failed to parse Qwen response: %w", err)
	}

	// 1. 题目类型提取
	questionType, err := extractQuestionType(rawData.QuText)
	if err != nil {
		return nil, err
	}

	// 2. 题干内容提取
	questionText := extractQuestionText(rawData.QuText)

	// 3. 选项内容直接使用
	options := rawData.Options

	// 构建解析后的数据结构
	qwenData := &model.QwenData{
		QuestionType: questionType,
		QuestionText: questionText,
		Options:      options,
	}

	return qwenData, nil
}

// extractQuestionType 从qutext中提取题目类型
func extractQuestionType(qutext string) (string, error) {
	// 匹配括号内的题目类型：(判断题)、(单选题)、(多选题)
	typeRegex := regexp.MustCompile(`^\(([^)]+)\)`)
	matches := typeRegex.FindStringSubmatch(qutext)

	if len(matches) < 2 {
		return "", fmt.Errorf("图片解析异常，请重新拍摄")
	}

	questionType := matches[1]

	// 验证题目类型是否有效
	validTypes := []string{"判断题", "单选题", "多选题"}
	for _, validType := range validTypes {
		if questionType == validType {
			return questionType, nil
		}
	}

	return "", fmt.Errorf("图片解析异常，请重新拍摄")
}

// extractQuestionText 从qutext中提取题干内容
func extractQuestionText(qutext string) string {
	// 去除开头的题目类型标记和序号
	// 匹配模式：(题目类型)数字、符号（包括中文句号）
	cleanRegex := regexp.MustCompile(`^\([^)]+\)\d+[、.．。]\s*`)
	cleanedText := cleanRegex.ReplaceAllString(qutext, "")

	return strings.TrimSpace(cleanedText)
}

// CleanQuestionFields 清洗题目字段并生成拼接字符串
// 根据S8.md文档的要求进行符号清洗
func CleanQuestionFields(qwenData *model.QwenData) (string, error) {
	if qwenData == nil {
		return "", fmt.Errorf("qwenData cannot be nil")
	}

	// 清洗题干内容
	cleanedQuestionText := cleanAllSymbols(qwenData.QuestionText)

	// 🚀 性能优化：预分配切片容量，使用更高效的排序
	keys := make([]string, 0, len(qwenData.Options))
	for key := range qwenData.Options {
		keys = append(keys, key)
	}

	// 🚀 优化：使用Go标准库的排序算法（更高效）
	sort.Strings(keys)

	// 🚀 性能优化：预估容量，减少内存重新分配
	estimatedSize := len(cleanedQuestionText) + len(qwenData.Options)*20 // 估算大小
	var result strings.Builder
	result.Grow(estimatedSize) // 预分配容量

	result.WriteString(cleanedQuestionText)
	for _, key := range keys {
		cleanedKey := cleanAllSymbols(key)
		cleanedValue := cleanAllSymbols(qwenData.Options[key])
		result.WriteString(cleanedKey)
		result.WriteString(cleanedValue)
	}

	return result.String(), nil
}

// cleanAllSymbols 清洗字符串中的换行符、制表符、空格和中英文标点符号
func cleanAllSymbols(text string) string {
	if text == "" {
		return ""
	}

	// 定义需要清洗的符号正则表达式
	// 包括：换行符、制表符、空格、中英文标点符号
	symbolRegex := regexp.MustCompile(`[\r\n\t\s，。！？；：""''（）()【】\[\]《》<>、,\.!\?;:"'\(\)\[\]]+`)

	// 清洗所有符号
	cleaned := symbolRegex.ReplaceAllString(text, "")

	return cleaned
}

// FormatQwenData 新的主入口方法
// 整合ParseQuestionJSON和CleanQuestionFields两个步骤
func FormatQwenData(qwenResponse string) (*model.QwenData, string, error) {
	// 第一步：解析JSON数据
	qwenData, err := ParseQuestionJSON(qwenResponse)
	if err != nil {
		return nil, "", err
	}

	// 第二步：清洗字段并生成拼接字符串
	cleanedString, err := CleanQuestionFields(qwenData)
	if err != nil {
		return nil, "", err
	}

	return qwenData, cleanedString, nil
}

// setQuestionOptions 统一设置Question的选项字段
func setQuestionOptions(question *model.Question, options map[string]string) {
	for key, value := range options {
		if value != "" {
			switch key {
			case "A":
				question.OptionA = &value
			case "B":
				question.OptionB = &value
			case "C":
				question.OptionC = &value
			case "D":
				question.OptionD = &value
			case "Y":
				question.OptionY = &value
			case "N":
				question.OptionN = &value
			}
		}
	}
}

// ConvertQwenToQuestion 将Qwen数据转换为Question模型
func ConvertQwenToQuestion(qwenData *model.QwenData, imageURL string, qwenRaw string) *model.Question {
	var userImagePtr *string
	if imageURL != "" {
		userImagePtr = &imageURL
	}

	question := &model.Question{
		QuestionType: qwenData.QuestionType,
		QuestionText: qwenData.QuestionText,
		UserImage:    userImagePtr,
		IsVerified:   0,
	}

	// 设置选项 - 直接使用Options，避免重复转换
	setQuestionOptions(question, qwenData.Options)

	// 设置原始数据
	var qwenRawData model.JSONField
	if err := json.Unmarshal([]byte(qwenRaw), &qwenRawData); err == nil {
		question.QwenRaw = qwenRawData
	}

	// 设置解析后的数据
	qwenParsedData := make(model.JSONField)
	qwenParsedBytes, _ := json.Marshal(qwenData)
	json.Unmarshal(qwenParsedBytes, &qwenParsedData)
	question.QwenParsed = qwenParsedData

	return question
}

// BuildOptionsMap 构建选项映射
func BuildOptionsMap(question *model.Question) map[string]string {
	options := make(map[string]string)
	
	if question.OptionA != nil {
		options["A"] = *question.OptionA
	}
	if question.OptionB != nil {
		options["B"] = *question.OptionB
	}
	if question.OptionC != nil {
		options["C"] = *question.OptionC
	}
	if question.OptionD != nil {
		options["D"] = *question.OptionD
	}
	if question.OptionY != nil {
		options["Y"] = *question.OptionY
	}
	if question.OptionN != nil {
		options["N"] = *question.OptionN
	}
	
	return options
}

// ConvertToQuestionResponse 将Question模型转换为响应格式
func ConvertToQuestionResponse(question *model.Question) *model.QuestionResponse {
	response := &model.QuestionResponse{
		ID:           question.ID,
		CacheKeyHash: question.CacheKeyHash,
		QuestionType: question.QuestionType,
		QuestionText: question.QuestionText,
		Options:      BuildOptionsMap(question),
		UserImage:    getUserImageString(question.UserImage),
		IsVerified:   fmt.Sprintf("%d", question.IsVerified),
	}

	// 设置问题对应的图片名称（如果存在）
	if question.ImageURL != nil {
		response.ImageURL = *question.ImageURL
	}
	// 注意：ImageURL字段默认为空字符串，无需显式设置

	// 设置解析
	if question.Analysis != nil {
		response.Analysis = *question.Analysis
	}

	// 设置答案
	if question.Answer != nil {
		answerMap := make(map[string]string)
		for key, value := range question.Answer {
			if strValue, ok := value.(string); ok {
				answerMap[key] = strValue
			}
		}
		response.Answer = answerMap
	}

	return response
}

// getUserImageString 获取用户图片字符串值
func getUserImageString(userImage *string) string {
	if userImage != nil {
		return *userImage
	}
	return ""
}
