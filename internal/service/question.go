package service

import (
	"context"
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/utils"
	"go-api-solve/pkg/redis"
	"log"
	"time"

	redisClient "github.com/redis/go-redis/v9"
)

// QuestionService 题目处理服务
type QuestionService struct {
	questionRepo       *repository.QuestionRepository
	qwenService        *QwenService
	deepseekService    *DeepseekService
	requestLogService  *RequestLogService
}

// NewQuestionService 创建新的题目服务
func NewQuestionService(
	questionRepo *repository.QuestionRepository,
	qwenService *QwenService,
	deepseekService *DeepseekService,
	requestLogService *RequestLogService,
) *QuestionService {
	return &QuestionService{
		questionRepo:      questionRepo,
		qwenService:       qwenService,
		deepseekService:   deepseekService,
		requestLogService: requestLogService,
	}
}

// ProcessImageQuestion 🚀 业务流程优化版本
func (s *QuestionService) ProcessImageQuestion(ctx context.Context, imageURL string) ([]model.QuestionResponse, error) {
	return s.ProcessImageQuestionOptimized(ctx, imageURL)
}

// ProcessImageQuestionOptimized 🚀 业务流程深度优化：合并步骤，减少等待
func (s *QuestionService) ProcessImageQuestionOptimized(ctx context.Context, imageURL string) ([]model.QuestionResponse, error) {
	var qwenTokens, deepseekTokens map[string]interface{}
	var finalResponses []model.QuestionResponse
	var processingError error

	// 🚀 优化1：异步图片验证，不阻塞主流程
	validationChan := make(chan error, 1)
	go func() {
		validationChan <- utils.ValidateImageURL(imageURL)
	}()

	// 🚀 优化2：立即开始Qwen处理，这是最耗时的操作
	qwenData, _, qwenResponse, err := s.qwenService.ProcessImage(ctx, imageURL)
	if err != nil {
		processingError = fmt.Errorf("failed to process image with qwen: %w", err)
		// 异步记录失败日志，不阻塞
		go s.logError(imageURL, processingError)
		return nil, processingError
	}

	// 🚀 优化3：并行处理多个任务
	type parallelResult struct {
		cacheKey      string
		qwenJsonClean string
		qwenTokens    map[string]interface{}
		qwenRawJSON   []byte
		validationErr error
	}

	resultChan := make(chan parallelResult, 1)
	go func() {
		var result parallelResult

		// 提取token信息
		if s.requestLogService != nil && qwenResponse != nil {
			result.qwenTokens = s.requestLogService.ExtractTokensFromQwenResponse(qwenResponse)
		}

		// 生成缓存键
		cacheKey, qwenJsonClean, err := s.generateCacheKeyFromQwenData(qwenData)
		if err != nil {
			log.Printf("Failed to generate cache key: %v", err)
			return
		}
		result.cacheKey = cacheKey
		result.qwenJsonClean = qwenJsonClean

		// 生成JSON
		result.qwenRawJSON, _ = json.Marshal(qwenResponse)

		// 检查图片验证结果
		select {
		case validationErr := <-validationChan:
			result.validationErr = validationErr
		default:
			// 验证还在进行中，不等待
		}

		resultChan <- result
	}()

	// 等待并行任务完成
	result := <-resultChan
	if result.cacheKey == "" {
		return nil, fmt.Errorf("failed to generate cache key")
	}

	qwenTokens = result.qwenTokens
	cacheKey := result.cacheKey
	qwenJsonClean := result.qwenJsonClean
	qwenRawJSON := result.qwenRawJSON

	// 记录图片验证结果（非阻塞）
	if result.validationErr != nil {
		log.Printf("Image validation failed (non-blocking): %v", result.validationErr)
	}

	// 🚀 优化4：合并缓存和数据库查询，减少等待时间
	cachedData, dbQuestions, err := s.getDataFromCacheOrDB(ctx, cacheKey)
	if err != nil {
		processingError = fmt.Errorf("failed to query cache and database: %w", err)
		go s.logError(imageURL, processingError)
		return nil, processingError
	}

	// 🚀 优化5：如果有缓存数据，立即返回
	if cachedData != nil {
		log.Printf("Cache hit for key: %s", cacheKey)
		// 异步记录日志，不阻塞返回
		go func() {
			if s.requestLogService != nil {
				s.requestLogService.LogAPIRequest(context.Background(), imageURL, cachedData, qwenTokens, nil, 1, "")
			}
		}()
		return cachedData, nil
	}

	// 🚀 优化6：如果有数据库数据，异步回写缓存并返回
	if len(dbQuestions) > 0 {
		log.Printf("Database hit for cache key: %s", cacheKey)
		finalResponses = s.convertQuestionsToResponses(dbQuestions)

		// 异步回写Redis，不阻塞返回
		go func() {
			if err := s.WriteToRedis(context.Background(), cacheKey, finalResponses); err != nil {
				log.Printf("Failed to write to redis: %v", err)
			}
		}()

		// 异步记录日志
		go func() {
			if s.requestLogService != nil {
				s.requestLogService.LogAPIRequest(context.Background(), imageURL, finalResponses, qwenTokens, nil, 1, "")
			}
		}()

		return finalResponses, nil
	}

	// 🚀 优化7：数据不存在，调用DeepSeek并并行处理后续操作
	log.Printf("No data found, calling DeepSeek for cache key: %s", cacheKey)

	// 将qwenData转换为JSON字符串传递给DeepSeek
	qwenParsedJSON, _ := json.Marshal(qwenData)
	deepseekData, deepseekRaw, deepseekResponse, err := s.deepseekService.ProcessQuestion(ctx, string(qwenParsedJSON))
	if err != nil {
		processingError = fmt.Errorf("failed to process question with deepseek: %w", err)
		go s.logError(imageURL, processingError)
		return nil, processingError
	}

	// 🚀 优化8：并行处理DeepSeek后续操作
	type deepseekResult struct {
		responses      []model.QuestionResponse
		deepseekTokens map[string]interface{}
		err            error
	}

	deepseekChan := make(chan deepseekResult, 1)
	go func() {
		var dsResult deepseekResult

		// 提取DeepSeek token信息
		if s.requestLogService != nil && deepseekResponse != nil {
			dsResult.deepseekTokens = s.requestLogService.ExtractTokensFromDeepseekResponse(deepseekResponse)
		}

		// 保存到数据库并获取响应
		responses, err := s.SaveDeepseekToDatabase(ctx, qwenData, deepseekData, imageURL, string(qwenRawJSON), deepseekRaw, cacheKey, qwenJsonClean)
		dsResult.responses = responses
		dsResult.err = err

		deepseekChan <- dsResult
	}()

	// 等待数据库保存完成
	dsResult := <-deepseekChan
	if dsResult.err != nil {
		processingError = fmt.Errorf("failed to save deepseek data to database: %w", dsResult.err)
		go s.logError(imageURL, processingError)
		return nil, processingError
	}

	finalResponses = dsResult.responses
	deepseekTokens = dsResult.deepseekTokens

	// 🚀 优化9：异步处理缓存写入和日志记录，立即返回结果
	go func() {
		// 写入Redis缓存
		if err := s.WriteToRedis(context.Background(), cacheKey, finalResponses); err != nil {
			log.Printf("Failed to write to redis after deepseek processing: %v", err)
		}

		// 记录成功日志
		if s.requestLogService != nil {
			s.requestLogService.LogAPIRequest(context.Background(), imageURL, finalResponses, qwenTokens, deepseekTokens, 1, "")
		}
	}()

	return finalResponses, nil
}

// 🗑️ 已移除旧的generateCacheKey方法

// generateCacheKeyFromQwenData 基于qwen_json_clean生成缓存键
// 🚀 性能优化：直接使用已解析的qwenData，避免重复解析
func (s *QuestionService) generateCacheKeyFromQwenData(qwenData *model.QwenData) (string, string, error) {
	// 1. 直接使用已解析的qwenData生成qwen_json_clean
	qwenJsonClean, err := utils.CleanQuestionFields(qwenData)
	if err != nil {
		return "", "", fmt.Errorf("failed to clean question fields: %w", err)
	}

	// 📝 日志3：第二次解析的纯文本
	log.Printf("=== 📝 日志3：第二次解析的纯文本 ===")
	log.Printf("QwenJsonClean: %s", qwenJsonClean)

	// 2. 基于qwen_json_clean生成缓存键
	cacheKey := utils.GenerateCacheKeyFromCleanString(qwenJsonClean)

	// 📝 缓存键生成日志
	log.Printf("=== 📝 缓存键生成日志 ===")
	log.Printf("CacheKey: %s", cacheKey)
	log.Printf("CacheKeyHash (存储到数据库): %s", cacheKey)

	return cacheKey, qwenJsonClean, nil
}

// 🚀 业务流程优化：合并缓存和数据库查询
func (s *QuestionService) getDataFromCacheOrDB(ctx context.Context, cacheKey string) ([]model.QuestionResponse, []model.Question, error) {
	// 并行查询Redis和MySQL
	type cacheResult struct {
		data []model.QuestionResponse
		err  error
	}
	type dbResult struct {
		questions []model.Question
		err       error
	}

	cacheChan := make(chan cacheResult, 1)
	dbChan := make(chan dbResult, 1)

	// 并行查询Redis
	go func() {
		data, err := s.getFromRedis(ctx, cacheKey)
		cacheChan <- cacheResult{data: data, err: err}
	}()

	// 并行查询MySQL
	go func() {
		questions, err := s.questionRepo.GetByCacheKeyHash(cacheKey)
		dbChan <- dbResult{questions: questions, err: err}
	}()

	// 等待Redis结果
	cacheRes := <-cacheChan
	if cacheRes.err == nil && cacheRes.data != nil {
		// 缓存命中，直接返回，不等待数据库查询
		return cacheRes.data, nil, nil
	}

	// 缓存未命中，等待数据库结果
	dbRes := <-dbChan
	if dbRes.err != nil {
		return nil, nil, dbRes.err
	}

	return nil, dbRes.questions, nil
}

// 🚀 业务流程优化：异步错误日志记录
func (s *QuestionService) logError(imageURL string, err error) {
	if s.requestLogService != nil {
		s.requestLogService.LogAPIRequest(context.Background(), imageURL, nil, nil, nil, 0, err.Error())
	}
}

// getFromRedis 从Redis获取缓存数据
// 🚀 性能优化：添加连接池复用和错误处理优化
func (s *QuestionService) getFromRedis(ctx context.Context, cacheKey string) ([]model.QuestionResponse, error) {
	// 使用pipeline减少网络往返
	pipe := redis.GetClient().Pipeline()
	getCmd := pipe.Get(ctx, cacheKey)

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		if err == redisClient.Nil {
			return nil, fmt.Errorf("cache miss")
		}
		return nil, err
	}

	data, err := getCmd.Result()
	if err != nil {
		if err == redisClient.Nil {
			return nil, fmt.Errorf("cache miss")
		}
		return nil, err
	}

	var responses []model.QuestionResponse
	if err := json.Unmarshal([]byte(data), &responses); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	return responses, nil
}

// convertQuestionsToResponses 将Question模型转换为响应格式
func (s *QuestionService) convertQuestionsToResponses(questions []model.Question) []model.QuestionResponse {
	responses := make([]model.QuestionResponse, len(questions))
	for i, question := range questions {
		responses[i] = *utils.ConvertToQuestionResponse(&question)
	}
	return responses
}

// WriteToRedis 回写Redis的方法
// 🚀 性能优化：使用pipeline批量操作，减少网络往返
func (s *QuestionService) WriteToRedis(ctx context.Context, cacheKey string, responses []model.QuestionResponse) error {
	// 将响应数据序列化为JSON
	jsonData, err := json.Marshal(responses)
	if err != nil {
		return fmt.Errorf("failed to marshal responses to JSON: %w", err)
	}

	// 使用pipeline批量操作
	pipe := redis.GetClient().Pipeline()

	// 设置缓存，过期时间为24小时
	expiration := 24 * time.Hour
	pipe.Set(ctx, cacheKey, string(jsonData), expiration)

	// 可以添加其他相关操作，如统计信息
	// pipe.Incr(ctx, "cache_writes_count")

	// 执行pipeline
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to set cache: %w", err)
	}

	log.Printf("Successfully wrote %d responses to Redis with key: %s", len(responses), cacheKey)
	return nil
}

// SaveDeepseekToDatabase 拆分DeepSeek返回数据并写入数据库
// 根据SaveDeepseekToDatabase.md文档的要求实现
func (s *QuestionService) SaveDeepseekToDatabase(
	ctx context.Context,
	qwenData *model.QwenData,
	deepseekData *model.DeepseekData,
	imageURL string,
	qwenRaw string,
	deepseekRaw string,
	cacheKey string,
	qwenJsonClean string,
) ([]model.QuestionResponse, error) {
	// 创建Question模型
	question := utils.ConvertQwenToQuestion(qwenData, imageURL, qwenRaw)

	// 设置缓存键（根据S7.md要求：cache_key_hash字段的值需要与redis的键值一致）
	question.CacheKeyHash = cacheKey

	// 注意：image_url字段保持为空，由管理员后期手动添加

	// 设置DeepSeek返回的数据
	var deepseekRawData model.JSONField
	if err := json.Unmarshal([]byte(deepseekRaw), &deepseekRawData); err == nil {
		question.DeepseekRaw = deepseekRawData
	}

	// 设置答案（JSON格式）
	if deepseekData.Answer != nil {
		answerData := make(model.JSONField)
		for key, value := range deepseekData.Answer {
			answerData[key] = value
		}
		question.Answer = answerData
	}

	// 设置解析
	if deepseekData.Analysis != "" {
		question.Analysis = &deepseekData.Analysis
	}

	// 🚀 性能优化：使用事务确保数据一致性，避免N+1查询
	var questions []model.Question
	err := s.questionRepo.CreateAndGetByCacheKey(question, cacheKey, &questions)
	if err != nil {
		return nil, fmt.Errorf("failed to save question to database: %w", err)
	}

	log.Printf("Successfully saved question to database with cache key: %s", cacheKey)

	// 转换为响应格式
	responses := s.convertQuestionsToResponses(questions)

	return responses, nil
}
