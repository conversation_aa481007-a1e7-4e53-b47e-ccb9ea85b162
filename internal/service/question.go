package service

import (
	"context"
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/utils"
	"go-api-solve/pkg/redis"
	"log"
	"time"

	redisClient "github.com/redis/go-redis/v9"
)

// QuestionService 题目处理服务
type QuestionService struct {
	questionRepo       *repository.QuestionRepository
	qwenService        *QwenService
	deepseekService    *DeepseekService
	requestLogService  *RequestLogService
}

// NewQuestionService 创建新的题目服务
func NewQuestionService(
	questionRepo *repository.QuestionRepository,
	qwenService *QwenService,
	deepseekService *DeepseekService,
	requestLogService *RequestLogService,
) *QuestionService {
	return &QuestionService{
		questionRepo:      questionRepo,
		qwenService:       qwenService,
		deepseekService:   deepseekService,
		requestLogService: requestLogService,
	}
}

// ProcessImageQuestion 处理图片题目的完整业务流程
func (s *QuestionService) ProcessImageQuestion(ctx context.Context, imageURL string) ([]model.QuestionResponse, error) {
	var qwenTokens, deepseekTokens map[string]interface{}
	var finalResponses []model.QuestionResponse
	var processingError error

	// 1. 验证图片是否有效可访问
	if err := utils.ValidateImageURL(imageURL); err != nil {
		processingError = fmt.Errorf("图片资源不存在，请重新上传")
		// 记录失败日志
		if s.requestLogService != nil {
			s.requestLogService.LogAPIRequest(ctx, imageURL, nil, nil, nil, 0, processingError.Error())
		}
		return nil, processingError
	}

	// 2. 调用Qwen模型处理图片
	qwenData, qwenContent, qwenResponse, err := s.qwenService.ProcessImage(ctx, imageURL)
	if err != nil {
		processingError = fmt.Errorf("failed to process image with qwen: %w", err)
		// 记录失败日志
		if s.requestLogService != nil {
			s.requestLogService.LogAPIRequest(ctx, imageURL, nil, nil, nil, 0, processingError.Error())
		}
		return nil, processingError
	}

	// 提取Qwen token信息
	if s.requestLogService != nil && qwenResponse != nil {
		qwenTokens = s.requestLogService.ExtractTokensFromQwenResponse(qwenResponse)
	}

	// 3. 格式化Qwen数据并生成缓存键
	cacheKey, qwenJsonClean, err := s.generateCacheKeyFromQwenData(qwenData, qwenContent)
	if err != nil {
		return nil, fmt.Errorf("failed to generate cache key: %w", err)
	}

	// 4. 生成完整的Qwen原始响应JSON用于数据库存储
	qwenRawJSON, _ := json.Marshal(qwenResponse)

	// 5. 查询Redis缓存
	cachedData, err := s.getFromRedis(ctx, cacheKey)
	if err == nil && cachedData != nil {
		log.Printf("Cache hit for key: %s", cacheKey)
		// 记录缓存命中的成功日志
		if s.requestLogService != nil {
			// 直接传递数组数据，而不是转换为map
			s.requestLogService.LogAPIRequest(ctx, imageURL, cachedData, qwenTokens, nil, 1, "")
		}
		return cachedData, nil
	}

	// 6. Redis不存在，查询MySQL
	// 根据S7.md要求：cache_key_hash字段的值需要与redis的键值一致
	questions, err := s.questionRepo.GetByCacheKeyHash(cacheKey)
	if err != nil {
		processingError = fmt.Errorf("failed to query database: %w", err)
		// 记录失败日志
		if s.requestLogService != nil {
			s.requestLogService.LogAPIRequest(ctx, imageURL, nil, qwenTokens, nil, 0, processingError.Error())
		}
		return nil, processingError
	}

	// 7. MySQL存在数据，回写Redis并返回
	if len(questions) > 0 {
		log.Printf("Database hit for cache key: %s", cacheKey)
		finalResponses = s.convertQuestionsToResponses(questions)

		// 回写Redis
		if err := s.WriteToRedis(ctx, cacheKey, finalResponses); err != nil {
			log.Printf("Failed to write to redis: %v", err)
		}

		// 记录数据库命中的成功日志
		if s.requestLogService != nil {
			// 直接传递数组数据，而不是转换为map
			s.requestLogService.LogAPIRequest(ctx, imageURL, finalResponses, qwenTokens, nil, 1, "")
		}

		return finalResponses, nil
	}

	// 8. MySQL不存在，调用DeepSeek模型
	log.Printf("No data found, calling DeepSeek for cache key: %s", cacheKey)

	// 将qwenData转换为JSON字符串传递给DeepSeek
	qwenParsedJSON, _ := json.Marshal(qwenData)
	deepseekData, deepseekRaw, deepseekResponse, err := s.deepseekService.ProcessQuestion(ctx, string(qwenParsedJSON))
	if err != nil {
		processingError = fmt.Errorf("failed to process question with deepseek: %w", err)
		// 记录失败日志
		if s.requestLogService != nil {
			s.requestLogService.LogAPIRequest(ctx, imageURL, nil, qwenTokens, nil, 0, processingError.Error())
		}
		return nil, processingError
	}

	// 提取DeepSeek token信息
	if s.requestLogService != nil && deepseekResponse != nil {
		deepseekTokens = s.requestLogService.ExtractTokensFromDeepseekResponse(deepseekResponse)
	}

	// 9. 保存DeepSeek数据到数据库
	finalResponses, err = s.SaveDeepseekToDatabase(ctx, qwenData, deepseekData, imageURL, string(qwenRawJSON), deepseekRaw, cacheKey, qwenJsonClean)
	if err != nil {
		processingError = fmt.Errorf("failed to save deepseek data to database: %w", err)
		// 记录失败日志
		if s.requestLogService != nil {
			s.requestLogService.LogAPIRequest(ctx, imageURL, nil, qwenTokens, deepseekTokens, 0, processingError.Error())
		}
		return nil, processingError
	}

	// 10. 回写Redis
	if err := s.WriteToRedis(ctx, cacheKey, finalResponses); err != nil {
		log.Printf("Failed to write to redis after deepseek processing: %v", err)
	}

	// 11. 记录成功日志
	if s.requestLogService != nil {
		// 直接传递数组数据，而不是转换为map
		s.requestLogService.LogAPIRequest(ctx, imageURL, finalResponses, qwenTokens, deepseekTokens, 1, "")
	}

	return finalResponses, nil
}

// generateCacheKey 生成缓存键（旧方法，保留兼容性）
func (s *QuestionService) generateCacheKey(qwenData *model.QwenData) (string, error) {
	return utils.GenerateCacheKey(qwenData)
}

// generateCacheKeyFromQwenData 基于qwen_json_clean生成缓存键
// 根据S7.md和S9.md要求：使用qwen_json_clean内容生成缓存键
func (s *QuestionService) generateCacheKeyFromQwenData(qwenData *model.QwenData, qwenContent string) (string, string, error) {
	// 1. 调用FormatQwenData获取qwen_json_clean
	// 注意：qwenContent现在是从Qwen API返回的content字符串
	_, qwenJsonClean, err := utils.FormatQwenData(qwenContent)
	if err != nil {
		return "", "", fmt.Errorf("failed to format qwen data: %w", err)
	}

	// 2. 基于qwen_json_clean生成缓存键
	cacheKey := utils.GenerateCacheKeyFromCleanString(qwenJsonClean)

	return cacheKey, qwenJsonClean, nil
}

// getFromRedis 从Redis获取缓存数据
func (s *QuestionService) getFromRedis(ctx context.Context, cacheKey string) ([]model.QuestionResponse, error) {
	data, err := redis.Get(ctx, cacheKey)
	if err != nil {
		if err == redisClient.Nil {
			return nil, fmt.Errorf("cache miss")
		}
		return nil, err
	}

	var responses []model.QuestionResponse
	if err := json.Unmarshal([]byte(data), &responses); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	return responses, nil
}

// convertQuestionsToResponses 将Question模型转换为响应格式
func (s *QuestionService) convertQuestionsToResponses(questions []model.Question) []model.QuestionResponse {
	responses := make([]model.QuestionResponse, len(questions))
	for i, question := range questions {
		responses[i] = *utils.ConvertToQuestionResponse(&question)
	}
	return responses
}

// WriteToRedis 回写Redis的方法
// 根据WriteToRedis.md文档的要求实现
func (s *QuestionService) WriteToRedis(ctx context.Context, cacheKey string, responses []model.QuestionResponse) error {
	// 将响应数据序列化为JSON
	jsonData, err := json.Marshal(responses)
	if err != nil {
		return fmt.Errorf("failed to marshal responses to JSON: %w", err)
	}

	// 设置缓存，过期时间为24小时
	expiration := 24 * time.Hour
	if err := redis.Set(ctx, cacheKey, string(jsonData), expiration); err != nil {
		return fmt.Errorf("failed to set cache: %w", err)
	}

	log.Printf("Successfully wrote %d responses to Redis with key: %s", len(responses), cacheKey)
	return nil
}

// SaveDeepseekToDatabase 拆分DeepSeek返回数据并写入数据库
// 根据SaveDeepseekToDatabase.md文档的要求实现
func (s *QuestionService) SaveDeepseekToDatabase(
	ctx context.Context,
	qwenData *model.QwenData,
	deepseekData *model.DeepseekData,
	imageURL string,
	qwenRaw string,
	deepseekRaw string,
	cacheKey string,
	qwenJsonClean string,
) ([]model.QuestionResponse, error) {
	// 创建Question模型
	question := utils.ConvertQwenToQuestion(qwenData, imageURL, qwenRaw)

	// 设置缓存键（根据S7.md要求：cache_key_hash字段的值需要与redis的键值一致）
	question.CacheKeyHash = cacheKey

	// 注意：image_url字段保持为空，由管理员后期手动添加

	// 设置DeepSeek返回的数据
	var deepseekRawData model.JSONField
	if err := json.Unmarshal([]byte(deepseekRaw), &deepseekRawData); err == nil {
		question.DeepseekRaw = deepseekRawData
	}

	// 设置答案（JSON格式）
	if deepseekData.Answer != nil {
		answerData := make(model.JSONField)
		for key, value := range deepseekData.Answer {
			answerData[key] = value
		}
		question.Answer = answerData
	}

	// 设置解析
	if deepseekData.Analysis != "" {
		question.Analysis = &deepseekData.Analysis
	}

	// 保存到数据库
	if err := s.questionRepo.Create(question); err != nil {
		return nil, fmt.Errorf("failed to save question to database: %w", err)
	}

	log.Printf("Successfully saved question to database with cache key: %s", cacheKey)

	// 查询相同cache_key_hash的所有记录
	questions, err := s.questionRepo.GetByCacheKeyHash(cacheKey)
	if err != nil {
		return nil, fmt.Errorf("failed to query questions after save: %w", err)
	}

	// 转换为响应格式
	responses := s.convertQuestionsToResponses(questions)

	return responses, nil
}
