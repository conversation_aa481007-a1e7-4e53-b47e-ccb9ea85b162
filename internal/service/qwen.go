package service

import (
	"context"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/utils"
	"go-api-solve/pkg/ai"
)

// QwenService Qwen服务
type QwenService struct {
	qwenClient           *ai.QwenClient
	modelConfigRepo      *repository.ModelConfigRepository
}

// NewQwenService 创建新的Qwen服务
func NewQwenService(qwenClient *ai.QwenClient, modelConfigRepo *repository.ModelConfigRepository) *QwenService {
	return &QwenService{
		qwenClient:      qwenClient,
		modelConfigRepo: modelConfigRepo,
	}
}

// ProcessImage 处理图片，调用Qwen模型进行识别
func (s *QwenService) ProcessImage(ctx context.Context, imageURL string) (*model.QwenData, string, *model.QwenResponse, error) {
	// 获取Qwen模型配置
	config, err := s.modelConfigRepo.GetByModelName("qwen-vl-plus")
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to get qwen model config: %w", err)
	}

	// 调用Qwen API
	response, err := s.qwenClient.CallQwenVLPlus(ctx, imageURL, config)
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to call qwen API: %w", err)
	}

	// 提取响应内容
	content, err := ai.ExtractContentFromQwenResponse(response)
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to extract content from qwen response: %w", err)
	}

	// 格式化Qwen数据
	qwenData, _, err := utils.FormatQwenData(content)
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to format qwen data: %w", err)
	}

	// 返回格式化的数据、原始内容和响应对象
	// 注意：这里返回的是content而不是完整的response JSON，因为我们需要content来生成缓存键
	return qwenData, content, response, nil
}
