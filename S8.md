这里是对FormatQwenData方法的逻辑进行更新描述：

qwen返回的响应数据格式与之前发生了变化。以下为最新示例；

{
    "qutext": "(单选题)20、如图所示，在这个位置时怎样使用灯光？",
    "options": {
        "A": "开启左转向灯",
        "B": "开启前照灯",
        "C": "开启危险报警闪光灯",
        "D": "开启右转向灯"
    }
}


{
    "qutext": "(多选题)12、同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
    "options": {
        "A": "前车正在左转弯的",
        "B": "前车正在上下乘客的",
        "C": "前车正在超车的",
        "D": "前车正在掉头的"
    }
}

{
    "qutext": "(判断题)4、驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
    "options": {
        "N": "错误",
        "Y": "正确"
    }
}

题库的数据表为core_questions，这是未来存储题库内容的地方；

格式化解析的第一步是内容拆分与提取（方法名定义为ParseQuestionJSON）；

1. 题目类型提取。
    qutext字段中的(判断题、单选题、多选题) 一共就这三种题型，这是题目的类型question_type，也是未来存储入库的字段 //若未识别到题型则返回图片解析异常，请重新拍摄。
2. 题干内容的提取；
    qutext字段中的括号和括号内的题目类型以及后面的数字和数字后面的符号都是不需要的，去掉这部分内容，剩下的就是题干内容question_text，这是未来存储入库的字段
3. 选项内容的提取；
    options字段内的内容就是选项，options字段是键值对的形式，其中键是选项的标识，值是选项的内容，需要分别提取，未来入库的时候会对应option_a, option_b, option_c, option_d, option_y, option_n 这几个字段。
4. 提取到内容生成一个新的结构体，示例如下；
{
    "qutext": "多选题",
    "qutext": "同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
    "options": {
        "A": "前车正在左转弯的",
        "B": "前车正在上下乘客的",
        "C": "前车正在超车的",
        "D": "前车正在掉头的"
    }
}

{
    "qutext": "判断题",
    "question_text": "驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
    "options": {
        "N": "错误",
        "Y": "正确"
    }
}





这里提取到的内容在下一步清洗处理后的后续业务还会复用，不可以丢弃，暂时我们将它定义为questio_parse字段；

格式化解析的第二部是符号清洗；（方法名定义为CleanQuestionFields）
1. 将上面提取到的question_type, question_text, option_a, option_b, option_c, option_d, option_y, option_n 这几个字段进行清洗，清洗的内容为去掉字段内容中的所有换行符，制表符，空格，以及中英文标点符号。仅常规中英文标点符号，特殊符号不需要清洗。

2. 将清洗后的数据进行拼接。留着备用

按如下示例演示
{
    "qutext": "(判断题)4、驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
    "options": {
        "N": "错误",
        "Y": "正确"
    }
}
最终应该得到的内容应该为；驾驶未安装制动防抱死装置ABS的机动车在冰雪路面行驶需要制动时应轻踏或间歇踩踏制动踏板N错误Y正确

我们给上述内容设置一个数据字段为qwen_json_clean,未来需要存入数据库